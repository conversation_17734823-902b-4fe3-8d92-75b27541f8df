<?php
$bookmark_asn_count = 0;
$wrong_asn_count = 0;
$bookmark_asn_arr = array();
$wrong_asn_arr = array();
foreach ($result as $qk => $question) {
	$bookmark_asn_count++;
	$bookmark_asn_arr[] = $qk;
}
foreach ($result2 as $qk2 => $question2) {
	$wrong_asn_count++;
	$wrong_asn_arr[] = $qk2;
}
$note_asn_count = count($question_notes);
$note_asn_arr = array_keys($question_notes);
$logged_in = $this->session->userdata('logged_in');
$premium = !in_array($logged_in["gid"], FREE_GROUP);
?>
<!-- comment section -->
<?php $this->load->view("components/comment/comment_section.php"); ?>
<!-- end comment section -->
<!-- Report Alert -->
<?php $this->load->view('custom/new_alert_report'); ?>
<!-- <PERSON>h <PERSON> -->
<div class="p-[16px] flex justify-between">
	<div class="flex items-center">
	<img src="<?= base_url() ?>images/icons/Document  Align Left 2.svg" alt="radio" class="!w-[24px] !h-[24px] !mr-[6px]">
	<span class="w-[250px] fit-content m-auto font-bold text-[20px] leading-6 text-black !leading-[30px]"><?= $this->lang->line('review') ?></span>
	</div>

	<div class="relative">
		<input type="text" id="inputSearch" class="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 bg-gray-50 mr-[10px]" placeholder="<?= $this->lang->line('search') ?>">
		<div class="absolute inset-y-0 left-0 flex items-center pl-3">
			<img src="<?php echo base_url(); ?>images/icons/search.svg" width="22px">
		</div>
	</div>
</div>
<!-- Tabbar -->
	<div class="mx-auto max-w-screen-lg flex justify-between">
		<ul class="inline-flex flex-wrap -mb-px text-sm font-medium text-center" id="myTab" data-tabs-toggle="#myTabContent" role="tablist">
			<li class="mr-8" role="presentation">
                <button class="focus:outline-none inline-block py-3 border-b-2 rounded-t-lg hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300" id="wrongQuestion-tab" onclick="change_tab('wrong')" data-tabs-target="#wrongQuestion" type="button" role="tab" aria-controls="wrongQuestion" aria-selected="false"><?= $this->lang->line('wrong_answer') ?> <span id="wrong_count">  <?php echo ' (' . $wrong_asn_count . ')' ?> </span></button>
            </li>
			<li class="mr-8" role="presentation">
				<button class="focus:outline-none inline-block py-3 border-b-2 rounded-t-lg" id="allQuestion-tab" data-tabs-target="#allQuestion" onclick="change_tab('bookmark')" type="button" role="tab" aria-controls="allQuestion" aria-selected="false"><?= $this->lang->line('bookmarked_answer') ?> <span id="bookmark_count">  <?php echo ' (' . $bookmark_asn_count . ')' ?> </span></button>
			</li>
			<li class="mr-8" role="presentation">
				<button class="focus:outline-none inline-block py-3 border-b-2 rounded-t-lg" id="noteQuestion-tab" data-tabs-target="#noteQuestion" onclick="change_tab('note')" type="button" role="tab" aria-controls="noteQuestion" aria-selected="false"><?= $this->lang->line('noted_question') ?> <span id="note_count">  <?php echo ' (' . $note_asn_count . ')' ?> </span></button>
			</li>
			<!-- <li class="mr-8" role="presentation">
				<button class="focus:outline-none inline-block py-3 border-b-2 rounded-t-lg" id="allQuestion-tab" data-tabs-target="#test" type="button" role="tab" aria-controls="test" aria-selected="false">test</button>
			</li> -->
		</ul>
		<label class="relative inline-flex items-center cursor-pointer" id="show-all-question">
			<input type="checkbox" value="" class="sr-only peer" id="show_all_checkbox">
			<div class="w-11 h-6 bg-gray-200 peer-focus:outline-none hover:ring-4 hover:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[13px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
			<span class="ml-3 text-sm font-medium text-gray-900 dark:text-gray-900"><?= $this->lang->line('show_all_question') ?></span>
		</label>
	</div>
	<div class="mb-4 border-b border-gray-200" style="margin-top: 1px;"></div>
	<div id="myTabContent">
		<div class="hidden p-4" id="allQuestion" role="tabpanel" aria-labelledby="allQuestion-tab">
		<?php if(count($result) == 0) : ?>
		<div class="empty-wrapper w-full">
			<div class="flex flex-col items-center">
				<img src="<?= base_url() ?>images/icons/no_result.svg" alt="No result">
				<div class="text-sm font-medium text-gray-800"><?= $this->lang->line('not_bookmarked') ?></div>
				<div class="mt-10">
					<!-- <button <?= $premium ? 'data-modal-target="practiceModal1" data-modal-toggle="practiceModal1"' : '' ?> type="button" class="practice text-white bg-primary-blue-1 mr-4 hover:bg-blue-400 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"><?= $this->lang->line('create_practice') ?></button> -->
					<a href="<?= base_url() ?>quiz"><button type="button" class="text-gray-800 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-800 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"><?= $this->lang->line('go_list_test') ?></button></a>
				</div>
			</div>
		</div>
		<?php endif ?>
    	</div>
        <div class="hidden p-4 rounded-lg" id="wrongQuestion" role="tabpanel" aria-labelledby="wrongQuestion-tab">
			<?php if(count($result2) == 0) : ?>
			<div class="empty-wrapper w-full">
				<div class="flex flex-col items-center">
					<img src="<?= base_url() ?>images/icons/no_result.svg" alt="No result">
					<div class="text-sm font-medium text-gray-800"><?= $this->lang->line('not_incorrect_answer') ?></div>
					<div class="mt-10">
						<!-- <button <?= $premium ? 'data-modal-target="practiceModal1" data-modal-toggle="practiceModal1"' : '' ?> type="button" class="practice text-white bg-primary-blue-1 mr-4 hover:bg-blue-400 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"><?= $this->lang->line('create_practice') ?></button> -->
						<a href="<?= base_url() ?>quiz"><button type="button" class="text-gray-800 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-800 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"><?= $this->lang->line('go_list_test') ?></button></a>
					</div>
				</div>
			</div>
			<?php endif ?>
		</div>

		<div class="hidden p-4 rounded-lg" id="noteQuestion" role="tabpanel" aria-labelledby="noteQuestion-tab">
			<?php if($note_asn_count == 0) : ?>
			<div class="empty-wrapper w-full">
				<div class="flex flex-col items-center">
					<img src="<?= base_url() ?>images/icons/no_result.svg" alt="No result">
					<div class="text-sm font-medium text-gray-800"><?= $this->lang->line('no_noted_question') ?></div>
					<div class="mt-10">
						<!-- <button <?= $premium ? 'data-modal-target="practiceModal1" data-modal-toggle="practiceModal1"' : '' ?> type="button" class="practice text-white bg-primary-blue-1 mr-4 hover:bg-blue-400 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"><?= $this->lang->line('create_practice') ?></button> -->
						<a href="<?= base_url() ?>quiz"><button type="button" class="text-gray-800 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-800 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600"><?= $this->lang->line('go_list_test') ?></button></a>
					</div>
				</div>
			</div>
			<?php endif ?>
		</div>
		<!-- <div id="test">
		</div> -->
		<!-- Practice modal -->
		<div id="practiceModal1" tabindex="-1" aria-hidden="true" class="fixed top-0 left-0 right-0 z-50 hidden w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
			<div class="relative w-full max-w-[745px] max-h-full">
				<!-- Modal content -->
				<div class="relative bg-white rounded-lg shadow py-4 px-6 dark:bg-gray-700">
					<!-- Modal header -->
					<div class="flex items-center justify-between mb-4 rounded-t">
						<div class="title text-gray-600 text-lg font-bold leading-[23.4px]">
							<?= $this->lang->line('practice') ?>
							<span class="text-sm font-medium text-gray-400 pl-1"><?= $category['category_name'] ?></span>
						</div>
						<button type="button" class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="practiceModal1">
							<svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
								<path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
							</svg>
							<span class="sr-only">Close modal</span>
						</button>
					</div>
					<!-- Modal body -->
					<!-- Basic -->
					<div class="basic_container p-4 rounded-lg border border-gray-200">
						<div class="flex mb-1">
							<img src="<?= base_url() ?>images/icons/Discovery 1.svg" alt="">
							<div class="text-base font-bold ml-1 text-gray-600"><?= $this->lang->line("basic") ?></div>
						</div>
						<div class="flex justify-between">
							<div class="wrap num_question w-[20.5%] shrink-0">
								<div class="text-sm font-medium mb-3 text-gray-900"><?= $this->lang->line("num_question") ?></div>
								<div class="flex relative justify-between">
									<div class="flex flex-col items-center z-10 num_item cursor-pointer selected">
										<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="10" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
										<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors">10</div>
									</div>
									<div class="flex flex-col items-center z-10 num_item cursor-pointer">
										<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="20" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
										<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors">20</div>
									</div>
									<div class="flex flex-col items-center z-10 num_item cursor-pointer">
										<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="30" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
										<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors">30</div>
									</div>
									<div class="flex flex-col items-center z-10 num_item cursor-pointer">
										<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="40" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
										<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors">40</div>
									</div>
									<div class="line_bar h-0.5 bg-gray-200 absolute top-[0.55rem]"></div>
								</div>
							</div>
							<div class="wrap test_time w-[73.3%] shrink-0">
								<div class="text-sm font-medium mb-3 text-gray-900"><?= $this->lang->line("time_minute") ?></div>
								<div class="flex justify-between relative">
									<div class="flex flex-col items-center z-10 num_item cursor-pointer">
										<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="10" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
										<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors">10</div>
									</div>
									<?php for ($i = 15; $i <= 55; $i += 5) : ?>
										<div class="flex flex-col items-center z-10 num_item cursor-pointer <?php if ($i == 30) echo 'selected'; ?>">
											<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="<?= $i ?>" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
											<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors"><?= $i ?></div>
										</div>
									<?php endfor ?>
									<div class="flex flex-col items-center z-10 num_item cursor-pointer">
										<div class="h-5 w-5 relative"><input id="default-radio-1" type="radio" value="60" name="default-radio" class="w-3 h-3 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-blue-600 bg-white border-gray-300 border-2 cursor-pointer focus:ring-0 focus:ring-offset-0 transition-all"></div>
										<div class="text-xs font-medium text-gray-600 mt-3 num_text transition-colors">60</div>
									</div>
									<div class="line_bar h-0.5 bg-gray-200 absolute top-[0.55rem]"></div>
								</div>
							</div>
						</div>
					</div>
					<!-- Advance -->
					<div class="advance_container p-4 rounded-lg border border-gray-200 mt-4 transition-all">
						<div class="flex justify-between section_toggle cursor-pointer">
							<div class="flex grow">
								<img width="17" src="<?= base_url() ?>images/icons/Add Category.svg" alt="">
								<div class="text-base font-bold text-gray-600 ml-1"><?= $this->lang->line("advance") ?></div>
							</div>
							<img width="16" class="transition-all origin-center expand_toggle" src="<?= base_url() ?>images/icons/chevron-down.svg" alt="">
						</div>

						<div class="advance_content hidden">
							<div class="mt-2 p-4 bg-gray-100 rounded-lg">
								<div class="flex">
									<div class="text-sm font-semibold text-gray-600 mr-1"><?= $this->lang->line("select_domain") ?></div>
									<img data-tooltip-target="tooltip-animation" src="<?= base_url() ?>images/icons/Information Circle (2).svg" alt="">
									<div id="tooltip-animation" role="tooltip" class="absolute z-10 max-w-[220px] invisible inline-block px-2 py-1 text-base font-normal text-white transition-opacity duration-300 bg-black rounded-sm shadow-sm opacity-0 tooltip dark:bg-gray-700">
										<?= $this->lang->line("domain_tooltip") ?>
										<div class="tooltip-arrow" data-popper-arrow></div>
									</div>
								</div>
								<div class="flex flex-wrap">
									<div class="domain_item all bg-white py-1 px-3 text-sm text-gray-900 font-medium border border-white transition-all mr-2 mt-2 rounded cursor-pointer selected" data="0"><?= $this->lang->line("all") ?></div>
									<?php 
									foreach (DOMAINS as $key => $domain) {
										if (${'show_assist_box_' . $key}) :
											foreach ($domain as $k => $v) : ?>
												<div class="domain_item bg-white py-1 px-3 text-sm text-gray-900 font-medium border border-white transition-all mr-2 mt-2 rounded cursor-pointer" data="<?= $k ?>"><?= $k ?></div>
											<?php endforeach;
										endif;
									}
									?>
								</div>
							</div>
							<div class="mt-2 p-4 bg-gray-100 rounded-lg">
								<div class="flex">
									<div class="text-sm font-semibold text-gray-600 mr-1"><?= $this->lang->line("characteristic") ?></div>
									<img data-tooltip-target="tooltip-animation1" src="<?= base_url() ?>images/icons/Information Circle (2).svg" alt="">
									<div id="tooltip-animation1" role="tooltip" class="absolute max-w-[220px] z-10 invisible inline-block px-2 py-1 text-base font-normal text-white transition-opacity duration-300 bg-black rounded-sm shadow-sm opacity-0 tooltip dark:bg-gray-700">
										<?= $this->lang->line("characteristic_tooltip") ?>
										<div class="tooltip-arrow" data-popper-arrow></div>
									</div>
								</div>
								<div class="flex flex-wrap mt-2">
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-horizon-1" value="all" name="inline-radio-group" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all" checked>
										<label for="radio-horizon-1" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("all") ?></label>
									</div>
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-horizon-2" value="notattempted" name="inline-radio-group" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-horizon-2" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("notattempted") ?></label>
									</div>
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-horizon-3" value="done_wrong" name="inline-radio-group" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-horizon-3" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("done_wrong") ?></label>
									</div>
									<div class="radio_item flex items-center">
										<input type="radio" id="radio-horizon-4" value="was_bookmark" name="inline-radio-group" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-horizon-4" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("was_bookmark") ?></label>
									</div>
								</div>
							</div>
							<div class="mt-2 p-4 bg-gray-100 rounded-lg">
								<div class="flex">
									<div class="text-sm font-semibold text-gray-600 mr-1"><?= $this->lang->line("difficulty") ?></div>
								</div>
								<div class="flex flex-wrap mt-2">
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-2" value="all" name="inline-radio-group-difficulty" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all" checked>
										<label for="radio-2" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("all") ?></label>
									</div>
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-3" value="easy" name="inline-radio-group-difficulty" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-3" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("easy") ?></label>
									</div>
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-4" value="medium" name="inline-radio-group-difficulty" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-4" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("medium") ?></label>
									</div>
									<div class="radio_item flex items-center mr-6">
										<input type="radio" id="radio-5" value="hard" name="inline-radio-group-difficulty" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-5" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("hard") ?></label>
									</div>
									<div class="radio_item flex items-center">
										<input type="radio" id="radio-6" value="very_hard" name="inline-radio-group-difficulty" class="w-4 h-4 hover:border-gray-200 text-blue-600 bg-white border-gray-400 border cursor-pointer mr-2 focus:ring-0 focus:ring-offset-0 transition-all">
										<label for="radio-6" class="text-sm font-medium text-gray-900 cursor-pointer"><?= $this->lang->line("very_hard") ?></label>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="flex"><button type="button" class="create-practice-btn text-white bg-primary-blue-1 hover:bg-blue-400 font-medium rounded-lg text-sm px-5 py-2.5 mt-4 mx-auto"><?= $this->lang->line("practice") ?></button></div>
				</div>
			</div>
		</div>
	</div>
	<!-- The Image Modal -->
	<div id="imageModal" tabindex="-1" class="h-full w-full fixed top-0 left-0 right-0 z-50 hidden" aria-hidden="true">
		<div class="flex justify-center items-center w-[1000px] max-h-full bg-white p-10 rounded-2xl overflow-y-auto" data-modal-hide="imageModal">
				<img class="modal-content w-full" id="modalImage">
			</div>
		</div>
	<!-- Report Modal -->
	<?php $this->load->view('custom/report_modal'); ?>
	<!-- note modal -->
	<div id="note-modal" tabindex="-1" class="fixed top-0 left-0 right-0 z-50 hidden p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full">
		<input type="hidden" id="note_id">
		<input type="hidden" id="question_id">
		<div class="relative w-full max-w-md max-h-full">
			<div class="relative bg-yellow-200 rounded-lg shadow dark:bg-gray-700 p-4">
				<div class="flex justify-between items-center">
					<div class="">
						<div class="text-xs font-medium text-[#90853B]" id="note_updated_at"><?= $this->lang->line("updated_at") ?>: <span id="note_updated_at_time"></span></div>
						<div class="animate-pulse shimmer">
							<div class="h-4 w-40 bg-gray-300 rounded"></div>
						</div>
					</div>
					<div class="">
						<button class="rounded-full p-1 bg-yellow-100 hidden" onClick="javascript:delete_note();" id="delete_note" type="button">
							<img src="<?= base_url() ?>images/icons/Delete 2.svg" alt="Delete">
						</button>
						<button class="close-note rounded-full p-1 bg-yellow-100" type="button">
							<img src="<?= base_url() ?>images/icons/Close.svg" alt="Close">
						</button>
					</div>
				</div>
				<textarea id="note" rows="14" class="hidden w-full px-0 text-sm font-medium text-gray-900 bg-yellow-200 border-0 dark:bg-gray-800 focus:ring-0 dark:text-white dark:placeholder-gray-400 resize-none" placeholder="<?= $this->lang->line('input_note') ?>" required /></textarea>
				<div class="animate-pulse shimmer mt-2">
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
					<div class="h-6 mb-2 w-full bg-gray-300 rounded"></div>
				</div>
			</div>
		</div>
	</div>
	<div class="max-w-[86%] mx-auto text-center flex flex-wrap justify-center pb-[15px] hidden" id="count_item">
		<p class="font-normal font-medium text-base leading-6 text-gray-600"><span id="current-item">1</span>/<span id="total-item"><?= $wrong_asn_count ?></span></p>
	</div>
<script>
	var translatedFlags = {};
	var bookmark_qid_list = "<?= $bookmark_qid_list ?>";
	var bookmarkArray = bookmark_qid_list == '' ? [] : bookmark_qid_list.split(",");
	var wrong_qid_list = "<?= $wrong_qid_list ?>";
	var wrongArray = wrong_qid_list.split(",");
	var current_tab = 'wrong';
	var show_all = false;
	var inputSearch = document.getElementById("inputSearch");
	var currentLang = "<?php echo $this->input->cookie('lang', true); ?>";
	var current_questions = [];
	var total_questions = 0;
	var isLoading = false;
	var searchTimeoutId;
	var listTranslate = [];

	function change_tab(tab){
		if(isLoading) return; // Prevent tab switching during loading

		listTranslate = [];
		inputSearch.value = "";
		$('#show_all_checkbox').prop('checked', false);
		current_tab = tab;
		current_index = 0;
		show_all = false;

		// Load questions for the new tab
		loadQuestions();
	}

	var current_index = 0;
	$('#show-all-question').click(function() {
		if(isLoading) return; // Prevent toggle during loading

		setTimeout(() => {
			var all = $('#show-all-question input').is(":checked");
			show_all = all;
			current_index = all ? -1 : 0;
			loadQuestions();
		}, 300);
	})
	function next_ques() {
		if(isLoading) return; // Prevent navigation during loading

		if (show_all || current_index >= total_questions - 1) {
			return;
		}

		current_index++;
		loadQuestions();
	}

	function previous_ques() {
		if(isLoading) return; // Prevent navigation during loading

		if (show_all || current_index <= 0) {
			return;
		}

		current_index--;
		loadQuestions();
	}

	$(document).keydown(function(event) {
		if(show_all == false && (typeof noteModal === 'undefined' || noteModal.isHidden())){
			if (event.which === 39) {
				next_ques();
			} else if (event.which === 37) {
				previous_ques();
			}
		}
	});

	function performSearch() {
		if(isLoading) return; // Prevent search during loading

		show_all = false;
		const checkbox = document.getElementById('show_all_checkbox');
		checkbox.checked = false;
		current_index = 0;
		loadQuestions();
	}

	inputSearch.addEventListener("keydown", function(event) {
		clearTimeout(searchTimeoutId);
		if (event.keyCode === 13) {
			performSearch();
		} else {
			// Create new timeout after 1.5 seconds
			searchTimeoutId = setTimeout(performSearch, 1500);
		}
	});
</script>
<script>
	function showTranslate(index) {
		const $target = $('#translate_content_' + index);
		const $arrow = $('#show_des_' + index);

		$arrow.toggleClass('rotate-180');

		if ($target.hasClass('hidden')) {
			$target.removeClass('hidden fade-down-exit fade-down-exit-active');
			$target.addClass('fade-down-enter');
			void $target[0].offsetWidth;
			$target.addClass('fade-down-enter-active');

			setTimeout(() => {
				$target.removeClass('fade-down-enter fade-down-enter-active');
			}, 300);
		} else {
			$target.removeClass('fade-down-enter fade-down-enter-active');
			$target.addClass('fade-down-exit');
			void $target[0].offsetWidth;
			$target.addClass('fade-down-exit-active');

			setTimeout(() => {
				$target.addClass('hidden');
				$target.removeClass('fade-down-exit fade-down-exit-active');
			}, 300);
		}
	}

	function translatedes(name, qid) {
		var sourceText = $('#description_'+ name).html();
		var sourceLang = 'en';
		var targetLang = 'vi';
		const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=${sourceLang}&tl=${targetLang}&dt=t&q=${encodeURI(
			sourceText,
		)}`;

		fetch(url)
			.then((response) => response.json())
			.then((json) => {
				$('#translate_result_'+ name).html(json[0].map((item) => item[0]).join(""));
				$('#translate_result_'+ qid).html(json[0].map((item) => item[0]).join(""));
			})
			.catch((error) => {
			});
	}

	function areListsEqual(list1, list2) {
		// Bước 1: Kiểm tra độ dài
		if (list1.length !== list2.length) {
			return false;
		}
		
		// Bước 2: Sắp xếp hai danh sách
		const sortedList1 = [...list1].sort();
		const sortedList2 = [...list2].sort();
		
		// Bước 3: So sánh từng phần tử
		for (let i = 0; i < sortedList1.length; i++) {
			if (sortedList1[i] !== sortedList2[i]) {
			return false;
			}
		}
		
		return true;
	}

	function handleDifficulty(difficulty){
		if(difficulty == "medium"){
			return "<?= $this->lang->line('medium') ?>";
		}else if(difficulty == "hard"){
			return "<?= $this->lang->line('hard') ?>";
		}else if(difficulty == "very_hard"){
			return "<?= $this->lang->line('very_hard') ?>";
		}else{
			return "<?= $this->lang->line('easy') ?>";
		}
	}

	function showLoading() {
		isLoading = true;
		// Hide question counter during loading
		$('#count_item').addClass('hidden');
		// Add loading spinner to the active tab content
		var activeTab = current_tab == 'wrong' ? '#wrongQuestion' :
						current_tab == 'note' ? '#noteQuestion' : '#allQuestion';
		$(activeTab).html('<div class="flex justify-center items-center h-64"><div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div></div>');
	}

	function hideLoading() {
		isLoading = false;
	}

	function loadQuestions() {
		if(isLoading) return;

		showLoading();

		var url, type, divID;
		var searchValue = inputSearch.value.trim();
		var index = show_all ? -1 : current_index;

		if(current_tab == 'wrong') {
			url = "<?= base_url() ?>Bookmarked_answers/get_wrong_questions";
			type = 1;
			divID = "wrongQuestion";
		} else if(current_tab == 'note') {
			url = "<?= base_url() ?>Bookmarked_answers/get_note_questions";
			type = 3;
			divID = "noteQuestion";
		} else {
			url = "<?= base_url() ?>Bookmarked_answers/get_bookmark_questions";
			type = 2;
			divID = "allQuestion";
		}

		$.ajax({
			url: url,
			type: "GET",
			data: {
				index: index,
				search: searchValue
			},
			beforeSend: function() {
				// Loading already shown
			},
			success: function(resp) {
				hideLoading();
				var data = typeof resp === 'string' ? JSON.parse(resp) : resp;

				if(data && (Array.isArray(data) || data.length !== undefined)) {
					current_questions = Array.isArray(data) ? data : [data];
					total_questions = current_questions.length;

					// Create questions first, then update UI
					createListCauSai(current_questions, null, type, divID);
					// Use setTimeout to ensure DOM elements are created before updating UI
					setTimeout(function() {
						updateUI();
					}, 100);
				} else {
					// No data found
					current_questions = [];
					total_questions = 0;
					updateUI();
					$('#' + divID).html('<div class="flex justify-center items-center h-64"><p class="text-gray-500">No questions found</p></div>');
				}
			},
			error: function(xhr, status, error) {
				hideLoading();
				console.error('Error loading questions:', error);
				$('#' + (current_tab == 'wrong' ? 'wrongQuestion' : current_tab == 'note' ? 'noteQuestion' : 'allQuestion'))
					.html('<div class="flex justify-center items-center h-64"><p class="text-red-500">Error loading questions</p></div>');
			}
		});
	}

	function updateUI() {
		// Update question counters
		var countElement = current_tab == 'wrong' ? '#wrong_count' :
						   current_tab == 'note' ? '#note_count' : '#bookmark_count';
		total_questions = current_tab == 'wrong' ? parseInt('<?= $wrong_asn_count ?>') :
						   current_tab == 'note' ? parseInt('<?= $note_count ?>') : parseInt('<?= $bookmark_count ?>');
		$(countElement).text('(' + total_questions + ')');

		// Update navigation
		if(show_all) {
			$('.previous-question').addClass('hidden');
			$('.next-question').addClass('hidden');
			$('#count_item').addClass('hidden');
		} else {
			$('.previous-question').removeClass('hidden');
			$('.next-question').removeClass('hidden');
			$('#count_item').removeClass('hidden');

			// Update current item display
			$('#current-item').text(total_questions > 0 ? current_index + 1 : 0);
			$('#total-item').text(total_questions);

			// Update navigation button states
			if(current_index <= 0) {
				$('.previous-question').addClass('disabled').removeClass('cursor-pointer');
			} else {
				$('.previous-question').removeClass('disabled').addClass('cursor-pointer');
			}

			if(current_index >= total_questions - 1) {
				$('.next-question').addClass('disabled').removeClass('cursor-pointer');
			} else {
				$('.next-question').removeClass('disabled').addClass('cursor-pointer');
			}
		}

		// Show/hide empty state for bookmark tab
		if(current_tab == 'bookmark') {
			if(total_questions > 0) {
				$('#allQuestion .empty-wrapper').addClass('hidden');
			} else {
				$('#allQuestion .empty-wrapper').removeClass('hidden');
			}
		}
	}

	// Initialize page
	$(document).ready(function() {
		loadQuestions();
	});

	var listTranslateWrong = [];
	var listTranslateBookmark = [];

	const notes = <?php echo json_encode($notes); ?>;
	// createListCauSai(result1,null,1,"wrongQuestion");
	// createListCauSai(result2,null,2,"allQuestion");
	// createListCauSai(questionNotes,null,3,"noteQuestion");
	function createListCauSai(result, search, type, divID){
		// Clear the target div
		$('#' + divID).empty();

		if(!result || result.length === 0) {
			$('#' + divID).html('<div class="flex justify-center items-center h-64"><p class="text-gray-500">No questions found</p></div>');
			return;
		}

		var abc = {
			'0': 'A', '1': 'B', '2': 'C', '3': 'D', '4': 'E',
			'5': 'F', '6': 'G', '7': 'H', '8': 'I', '9': 'J', '10': 'K'
		};

		var questions = Array.isArray(result) ? result : [result];
		var idList = 0;

		questions.forEach(function(question, qk) {
			var save_ans = question.userAns ? question.userAns.split(",").filter(ans => ans !== null && ans !== "") : [];
			var correct_ans = question.correct ? question.correct.split(",").filter(ans => ans !== null && ans !== "") : [];

			// Create main div element
			var divElement = document.createElement('div');
			divElement.classList.add('rqn', 'max-w-screen-lg', 'mx-auto', 'mb-12');
			divElement.setAttribute('name', question.qid);
			divElement.setAttribute('id', type == 3 ? 'qnnote'+ idList.toString() : type == 2 ? 'qn'+ idList.toString() :'qnw' + idList.toString());

			// Show all questions if show_all is true, otherwise show only first question
			if(show_all || idList == 0){
				divElement.style.display = 'block';
			} else {
				divElement.style.display = 'none';
			}
			// Tạo phần tử con trong divElement
			var innerDivElement = document.createElement('div');
			innerDivElement.setAttribute('id', type == 3 ? 'qnote'+ idList.toString() : type == 2 ? 'q'+ idList.toString() :'qw' + idList.toString());
			innerDivElement.style.display = '';

			// Tạo phần tử boxDetail
			var boxDetailElement = document.createElement('div');
			boxDetailElement.classList.add('boxDetail', 'p-5', 'rounded-2xl', 'shadow-lg', 'bg-white', 'relative', 'border', areListsEqual(save_ans, correct_ans) ? 'border-[#057A55]' : 'border-[#E02424]');

			// Tạo phần tử numQuestion
			var numQuestionElement = document.createElement('div');
			numQuestionElement.classList.add('numQuestion', 'text-base', 'font-semibold', 'text-black', 'mr-28');

			// Tạo phần tử span
			var spanElement = document.createElement('span');
			spanElement.style.lineHeight = '1.8';
			spanElement.setAttribute('id', type == 3 ? 'annote'+ idList.toString() : type == 2 ? 'an'+ idList.toString() :'anw' + idList.toString());
			spanElement.classList.add('question');
			var questionText = "";
			if (/^<p><br \/>.*/.test(question.question)) {
				questionText = question.question.substr(9, question.question.length - 4).replace(/<img([^>]*)>/, '<img$1 class="focusImage" data-modal-target="imageModal" data-modal-toggle="imageModal">');
			} else if(/<p>.*?<\/p>/.test(question.question)) {
				questionText = question.question.substr(3, question.question.length - 4).replace(/<img([^>]*)>/, '<img$1 class="focusImage" data-modal-target="imageModal" data-modal-toggle="imageModal">');
			} else if (/^<br>.*/.test(question.question)) {
				questionText = question.question.substr(4).replace(/<img([^>]*)>/, '<img$1 class="focusImage" data-modal-target="imageModal" data-modal-toggle="imageModal">');
			} else {
				questionText = question.question.replace(/<img([^>]*)>/, '<img$1 class="focusImage" data-modal-target="imageModal" data-modal-toggle="imageModal">')
			}
			spanElement.innerHTML = (current_index + 1).toString() + '. ' + questionText.replace(/(\.\.\/)+/g, '<?= base_url() ?>');

			// Tạo phần tử div chứa boxDetailResult
			var boxDetailResultDiv = document.createElement('div');
			boxDetailResultDiv.className = 'boxDetailResult';

			// Tạo phần tử div chứa mcanswersDiv
			var mcanswersDiv = document.createElement('div');
			if(question.description != "") {
				mcanswersDiv.className =  "mb-4 border-b border-dashed border-gray-300";
			}else{
				mcanswersDiv.className = "";
			}
			// Tạo các phần tử con cho mcanswersDiv
			if (question.question_type.toString() == "Câu hỏi một lựa chọn") {
				var i = 0;
				question.options.forEach(function(option, ok) {
					if (option.qid == question.qid) {
						var inputElement = document.createElement('input');
						inputElement.type = 'hidden';
						inputElement.name = 'question_type[]';
						inputElement.id = 'q_type' + idList;
						inputElement.value = '1';

						mcanswersDiv.appendChild(inputElement);

						if (save_ans.includes(option.oid)) {
							if (!correct_ans.includes(option.oid)){
								var opDiv = createOptionDiv('op flex items-start rounded-lg bg-red-600 py-2 px-3 my-4 relative items-center', 'radiobox-x',option.q_option, abc[i], "ml-1 text-base text-white flex", "");
							}else{
								var opDiv = createOptionDiv('op flex items-start rounded-lg bg-green-600 py-2 px-3 my-4 relative items-center', 'radiobox-v',option.q_option, abc[i], "ml-1 text-base text-white flex", "");
							}
						} else if (correct_ans.includes(option.oid))  {
							var opDiv = createOptionDiv('op flex items-start rounded-lg bg-green-600 py-2 px-3 my-4 relative items-center', 'radiobox', option.q_option, abc[i], "ml-1 text-base text-white flex", "radio-correct");
						}else{
							var opDiv = createOptionDiv('op flex items-start rounded-lg bg-white border border-gray-300 py-2 px-3 my-4 relative items-center', 'radiobox', option.q_option, abc[i], "ml-1 text-base text-gray-700 flex", "radio-normal");
						}
						mcanswersDiv.appendChild(opDiv);
						i += 1;
					} else {
						i = 0;
					}
				});
			}else if(question.question_type.toString() == "Câu hỏi nhiều lựa chọn"){
				var save_ans = question.userAns.split(",");
				var correct_ans = question.correct.split(",");
				var i = 0;
				question.options.forEach(function(option, ok) {
					if (option.qid == question.qid) {
						if (save_ans.includes(option.oid)) {
							if (!correct_ans.includes(option.oid)){
								var opDiv = createOptionDiv('op flex items-start rounded-lg bg-red-600 py-2 px-3 my-4 relative items-center', 'checkbox-x',option.q_option, abc[i], "ml-1 text-base text-white flex", "");
							}else{
								var opDiv = createOptionDiv('op flex items-start rounded-lg bg-green-600 py-2 px-3 my-4 relative items-center', 'checkbox-v',option.q_option, abc[i], "ml-1 text-base text-white flex", "");
							}
						} else if (correct_ans.includes(option.oid))  {
							var opDiv = createOptionDiv('op flex items-start rounded-lg bg-green-600 py-2 px-3 my-4 relative items-center', 'checkbox', option.q_option, abc[i], "ml-1 text-base text-white flex", "radio-correct");
						}else{
							var opDiv = createOptionDiv('op flex items-start rounded-lg bg-white border border-gray-300 py-2 px-3 my-4 relative items-center', 'checkbox', option.q_option, abc[i], "ml-1 text-base text-gray-700 flex", "radio-normal");
						}
						mcanswersDiv.appendChild(opDiv);
						i += 1;
					} else {
						i = 0;
					}
				});
			}else if (question.question_type.toString() == "Khớp cột") {
				var save_ans = question.userAns.split(",");
				var correct_ans = question.correct.split(",");
				var inputElement = document.createElement('input');
				inputElement.type = 'hidden';
				inputElement.name = 'question_type[]';
				inputElement.id = 'q_type' + idList;
				inputElement.value = '5';
				mcanswersDiv.appendChild(inputElement);	
				var i = 0;
				var match_1 = [];
				var match_2 = [];
				question.options.forEach((option) => {
					if (option.qid === question.qid) {
						match_1.push(option.q_option);
						match_2.push(option.q_option_match);
						i += 1;
					} else {
						i = 0;
					}
				});
				const table = document.createElement('table');
				table.style.width = '50%';

				const trHeader = document.createElement('tr');
				const tdHeader1 = document.createElement('td');
				const tdHeader2 = document.createElement('td');
				const tdHeader3 = document.createElement('td');
				tdHeader2.textContent = 'Your Answer';
				tdHeader3.textContent = 'Correct Answer';
				trHeader.appendChild(tdHeader1);
				trHeader.appendChild(tdHeader2);
				trHeader.appendChild(tdHeader3);
				table.appendChild(trHeader);

				match_1.forEach((mval, mk1) => {
				const tr = document.createElement('tr');
				const td1 = document.createElement('td');
				const td2 = document.createElement('td');
				const td3 = document.createElement('td');

				td1.textContent = abc[mk1] + ' ' + mval;
				match_2.forEach((mval2, mk2) => {
					const m1 = mval + '___' + mval2;
					if (save_ans.includes(m1)) {
					td2.textContent = mval2;
					}
				});

				td3.textContent = match_2[mk1];
				tr.appendChild(td1);
				tr.appendChild(td2);
				tr.appendChild(td3);
				table.appendChild(tr);
				});

				mcanswersDiv.appendChild(table);						
			} else if (question.question_type.toString()  == "Câu trả lời ngắn") {
				var save_ans = question.userAns.split(",");
				var correct_ans = question.correct.split(",");
				const input = document.createElement('input');
				input.type = 'hidden';
				input.name = 'question_type[]';
				input.id = 'q_type' + idList;
				input.value = '3';
				// Tạo một phần tử div và thêm nội dung tương ứng
				const opDiv1 = document.createElement('div');
				if (save_ans !== '') {
					const yourAnswerText = document.createElement('b');
					yourAnswerText.textContent = '<?= $this->lang->line('your_answer') ?>: ';
					const saveAnsText = document.createTextNode(save_ans);
					opDiv1.appendChild(yourAnswerText);
					opDiv1.appendChild(saveAnsText);
				} else {
					const unAnsweredText = document.createElement('b');
					unAnsweredText.style.color = 'red';
					unAnsweredText.textContent = '<?= $this->lang->line('UnAnswered') ?>';
					opDiv1.appendChild(unAnsweredText);
				}

				// Tạo một phần tử div và thêm nội dung tương ứng
				const opDiv2 = document.createElement('div');
				question.options.forEach((option) => {
					if (option.qid === question.qid) {
						const correctAnswerText = document.createElement('b');
						correctAnswerText.textContent = '<?= $this->lang->line('correct_answer') ?>: ' + option.q_option;
						opDiv2.appendChild(correctAnswerText);
					}
				});

				// Thêm các phần tử vào mcanswersDiv
				mcanswersDiv.appendChild(input);
				mcanswersDiv.appendChild(opDiv1);
				mcanswersDiv.appendChild(opDiv2);
			} else if (question.question_type.toString()  == "Câu trả lời dài") {
				const input = document.createElement('input');
				input.type = 'hidden';
				input.name = 'question_type[]';
				input.id = `q_type`+ idList;
				input.value = '4';
				mcanswersDiv.appendChild(inputElement);
				// Tạo một phần tử div và thêm các thuộc tính tương ứng
				const opDiv = document.createElement('div');
				opDiv.className = 'op';

				// Kiểm tra nếu có câu trả lời
				if (save_ans !== '') {
				const answerText = document.createElement('p');
				answerText.textContent = lang.line('answer');
				opDiv.appendChild(answerText);

				const wordCountText = document.createElement('p');
				wordCountText.textContent = lang.line('word_counts') + ' ' + str_word_count(save_ans);
				opDiv.appendChild(wordCountText);

				const textarea = document.createElement('textarea');
				textarea.name = 'answer['+ idList +'][]';
				textarea.id = `answer_value`+ idList;
				textarea.style.width = '100%';
				textarea.value = save_ans;
				textarea.addEventListener('keyup', function() {
					count_char(this.value, `char_count` + idList);
				});
				opDiv.appendChild(textarea);
				} else {
				const unAnsweredText = document.createElement('b');
				unAnsweredText.style.color = 'red';
				unAnsweredText.textContent = '<?= $this->lang->line('UnAnswered') ?>';
				opDiv.appendChild(unAnsweredText);
				}

				// Thêm phần tử vào mcanswersDiv
				mcanswersDiv.appendChild(opDiv);
			}
			// Thêm mcanswersDiv vào boxDetailResultDiv
			boxDetailResultDiv.appendChild(mcanswersDiv);


			// Tạo phần tử div description-box
			var descriptionDiv = document.createElement('div');
			descriptionDiv.className = 'description-box  py-2 px-4 bg-gray-100 mt-4 mb-4';

			// Tạo phần tử div des-title
			var desTitleDiv = document.createElement('div');
			desTitleDiv.className = 'des-title text-base text-gray-700 font-bold inline-block';
			desTitleDiv.innerHTML = '<?php echo $this->lang->line("description"); ?>';

			//tạo phần tử div translate
			var transDiv = document.createElement('div');
			// phần tử translate
			var button = document.createElement("button");
			var buttonText = document.createTextNode("<?= $this->lang->line('translate') ?>");
			var img = document.createElement("img");
			var div = document.createElement("div");
			var div2 = document.createElement("div");
			var p = document.createElement("p");
			var p2 = document.createElement("p");
			var img2 = document.createElement("img");
			var span = document.createElement("span");
			var spanText = document.createTextNode("Bản dịch này ");
			var test = document.createTextNode("Bản dịch tiếng Việt");
			var spanBold = document.createElement("span");
			spanBold.appendChild(document.createTextNode("KHÔNG"));
			var spanText2 = document.createTextNode(" được thực hiện bởi chuyên gia. Chỉ mang tính chất tham khảo.");

			// Thiết lập thuộc tính và nội dung cho các phần tử
			button.setAttribute("id", "translate"+ type +'_'+ qk);
			button.setAttribute("type", "button");
			button.setAttribute("class", "mt-[8px] mb-[6px] border-solid border-2 border-primary-blue-1 text-primary-blue-1 font-bold rounded text-sm px-[8px] py-[4px] text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800");
			//button.setAttribute("onclick", 'showTranslate('+ qk +')');
			<?php if ($translate_mode == 0): ?>
				button.classList.add("hidden");
			<?php endif; ?>
			
			if(currentLang == "vietnamese"){
				button.addEventListener('click', function () {
					const index = type + '_' + qk;

					if (!translatedFlags[index]) {
						translatedes(index, question.qid);
						translatedFlags[index] = true;
					}

					showTranslate(index);
				});
			}
			img.setAttribute("id", "show_des_" + type + '_' + qk);
			img.setAttribute("class", "inline ml-[4px] rotate-180");
			img.setAttribute("src", "<?= base_url() ?>images/icons/<?= SITE_ID == 2 ? 'itcertprep/' : '' ?>Up_arrow_blue.svg");
			img.setAttribute("alt", "Chevron right");
			div.setAttribute("id", "translate_content_"+ type +'_'+ qk);
			div.setAttribute("class", "w-full bg-[#E5E7EB] relative mt-[6px] rounded-lg p-[10px] hidden");
			// Tạo ra phần tử tam giác
			var triangle = document.createElement("div");
			triangle.setAttribute("class", "triangle");
			triangle.setAttribute("style", "position: absolute;top: -6px;left: 0;");
			p.setAttribute("class", "des-title text-gray-700 font-bold inline-block mb-[10px]");
			p.appendChild(test);
			p2.setAttribute("id", "translate_result_"+ type +'_'+ qk);
			div2.setAttribute("class", "mt-[10px] flex items-center");
			img2.setAttribute("class", "inline mr-[5px]");
			img2.setAttribute("src", "<?php echo base_url(); ?>/images/icons/Information Circle Yellow.svg");
			spanBold.setAttribute("class", "font-bold");

			// Append phần tử tam giác vào div
			div.appendChild(triangle);

			// Append nội dung vào các phần tử
			button.appendChild(buttonText);
			button.appendChild(img);
			p.appendChild(span);
			span.appendChild(spanText);
			span.appendChild(spanBold);
			span.appendChild(spanText2);
			div2.appendChild(img2);
			div2.appendChild(span);
			

			// Append các phần tử vào biến transdiv
			transDiv.appendChild(button);
			transDiv.appendChild(div);
			div.appendChild(triangle);
			div.appendChild(p);
			div.appendChild(p2);
			div.appendChild(div2);

			// Tạo phần tử div des-content
			var desContentDiv = document.createElement('div');
			desContentDiv.className = 'des-content text-base font-medium text-black';
			desContentDiv.innerHTML = question.description;
			desContentDiv.id = 'description_'+ type +'_'+ qk;

			// Thêm phần tử des-title và des-content vào description-box
			if(question.description != ""){
				descriptionDiv.appendChild(desTitleDiv);
				descriptionDiv.appendChild(desContentDiv);
				if(currentLang == "vietnamese"){
					descriptionDiv.appendChild(transDiv);
				}
			}

			// Tạo phần tử div max-w-[86%]
			var maxDiv = document.createElement('div');
			maxDiv.className = 'max-w-[86%] inline-block text-center flex flex-wrap justify-center';
			if(question.count != null){
				// Tạo phần tử img
				var imgError = document.createElement('img');
				imgError.className = 'pr-[5px] mb-[1px] inline-block';
				imgError.src = '<?= base_url() ?>images/icons/warning_red.svg';
				imgError.alt = 'checkbox';

				// Tạo phần tử span
				var wrongNum = document.createElement('span');
				wrongNum.className = 'font-semibold text-base leading-6 text-red-600';
				wrongNum.innerHTML = '<?= $this->lang->line("no_times_incorrected") . ": " ?>' + question.count;

				// Thêm phần tử img và span vào max-w-[86%]
				maxDiv.appendChild(imgError);
				maxDiv.appendChild(wrongNum);
			}
			//Tạo phần tử Difficulty
			var difficultyDiv = document.createElement('div');
			difficultyDiv.className = 'inline-block';
			// Tạo phần tử img
			var imgDifficulty = document.createElement('img');
			imgDifficulty.className = 'inline-block pr-[5px] w-[25px] mb-[2px]';
			imgDifficulty.src = '<?= base_url() ?>images/icons/speedometer.svg';
			imgDifficulty.alt = 'circle';

			// Tạo phần tử span
			var difficultyType = document.createElement('span');
			difficultyType.className = 'font-bold leading-6 text-[#4B5563] text-[16px] inline-block';
			difficultyType.innerHTML = '<?= $this->lang->line('difficulty') . ": "?>' + handleDifficulty(question.difficulty);

			// Thêm phần tử img và span vào max-w-[86%]
			difficultyDiv.appendChild(imgDifficulty);
			difficultyDiv.appendChild(difficultyType);

			//Tạo phần tử Disable
			var disableDiv = document.createElement('div');
			disableDiv.className = 'inline-block';
			// Tạo phần tử img
			var imgDisable = document.createElement('img');
			imgDisable.className = 'inline-block pr-[2px] w-[25px] mb-[2px]';
			imgDisable.src = '<?= base_url() ?>images/icons/Information Square.svg';
			imgDisable.alt = 'square';

			// Tạo phần tử span
			var disableType = document.createElement('span');
			disableType.className = 'font-bold leading-6 text-[#4B5563] text-[16px] inline-block';
			disableType.innerHTML = '<?= $this->lang->line("question_disable") ?>';

			// Thêm phần tử img và span vào max-w-[86%]
			disableDiv.appendChild(imgDisable);
			disableDiv.appendChild(disableType);

			//Thêm phần tử seperator
			var seperatorDiv = document.createElement('div');
			seperator = document.createElement('p');
			seperator.className = 'inline-block pl-[15px] pr-[15px] pt-[3px] text-[#D1D5DB] text-[12px]';
			seperator.textContent = '|';
			if(question.count != null){
				seperatorDiv.appendChild(seperator);
			}
			// Tạo các phần tử HTML button reporta
			var flexDiv = document.createElement("div");
			flexDiv.className = "flex absolute right-[100px] top-5 rounded-lg";
			var reportButton = document.createElement("button");
			reportButton.id = "report" + question.qid;
			// Thêm sự kiện onclick bằng addEventListener
			reportButton.addEventListener('click', function() {
				openDetail(question.qid);
			});
			reportButton.setAttribute("name", "question"+ qk);
			reportButton.className = "report_button";
			var reportImg = document.createElement("img");
			reportImg.src = "<?= base_url() ?>images/icons/Message_report.svg";
			reportImg.alt = "Danger Triangle";
			reportButton.appendChild(reportImg);
			flexDiv.appendChild(reportButton);

			// Tạo các phần tử HTML button bookmark
				var flexDivBookMark = document.createElement("div");
				flexDivBookMark.className = "flex absolute right-5 top-5 rounded-lg";
				var bookmarkButton = document.createElement("button");
				// Thêm sự kiện onclick bằng addEventListener
				bookmarkButton.addEventListener('click', function() {
					addBookmark(question.qid, question.rid, type);
				});
				bookmarkButton.setAttribute("name", "bookmark"+ qk);
				var bookmarkImg = document.createElement("img");
				if(bookmarkArray.includes(question.qid)){
					//bookmarkImg.className = "cursor-default";
					const SITE_ID = <?= SITE_ID ?>;
					if (SITE_ID == 2) {
						bookmarkImg.src = "<?= base_url() ?>images/icons/itcertprep/Bookmark 2.svg";
					} else {
						bookmarkImg.src = "<?= base_url() ?>images/icons/Bookmark 2.svg";
					}
				}else{
					bookmarkImg.src = "<?= base_url() ?>images/icons/Bookmark_2.svg";	
				}
				bookmarkImg.alt = "Bookmark 2";
				if(type == 1)
				bookmarkImg.id =  "bookmark_wrong" + question.qid;
				else if(type == 3)
				bookmarkImg.id =  "bookmark_note" + question.qid;
				else
				bookmarkImg.id =  "bookmark" + question.qid;
				bookmarkButton.appendChild(bookmarkImg);
				flexDivBookMark.appendChild(bookmarkButton);

			// Tạo các phần tử HTML button note
			var flexDivnote = document.createElement("div");
				flexDivnote.className = "flex absolute right-[60px] top-5";
				var noteButton = document.createElement("button");
				noteButton.className = "note-btn note" + question.qid + " ml-4 w-6 cursor-pointer";
				var noteId = notes[question.qid] == undefined ? 0 : notes[question.qid];
				noteButton.setAttribute("data-id", noteId);
				noteButton.setAttribute("data-qid", question.qid);
				noteButton.setAttribute("data-modal-target", "note-modal");
				noteButton.style.pointerEvents = "auto";
				noteButton.style.zIndex = "10";
				var noteImg = document.createElement("img");
				var hasNoteImg = document.createElement("img");

				noteImg.className = noteId != 0 ? "note hidden" : "note";
				noteImg.src = "<?= base_url() ?>images/icons/take note.svg";

				hasNoteImg.className = noteId == 0 ? "noted hidden" : "noted";
				hasNoteImg.src = "<?= base_url() ?>images/icons/has note.svg";
				noteImg.alt = "Note";
				hasNoteImg.alt = "Note";

				noteButton.appendChild(noteImg);
				noteButton.appendChild(hasNoteImg);
				flexDivnote.appendChild(noteButton);

			var prevQuestionDiv = document.createElement("div");
			prevQuestionDiv.className = "previous-question absolute -left-20 top-0 pt-[15%] pb-[10%]";
			prevQuestionDiv.onclick = function() {
				previous_ques();
			};
			var prevQuestionImg = document.createElement("img");
			prevQuestionImg.src = "<?= base_url() ?>images/icons/Right 3.svg";
			prevQuestionImg.alt = "previous";
			prevQuestionDiv.appendChild(prevQuestionImg);

			var nextQuestionDiv = document.createElement("div");
			nextQuestionDiv.className = "next-question absolute -right-20 top-0 pt-[15%] pb-[10%] cursor-pointer";
			nextQuestionDiv.onclick = function() {
				next_ques();
			};
			var nextQuestionImg = document.createElement("img");
			nextQuestionImg.src = "<?= base_url() ?>images/icons/Right 3.svg";
			nextQuestionImg.alt = "next";
			nextQuestionDiv.appendChild(nextQuestionImg);

			// phần tử report
			var reportDiv = document.createElement("div");
			reportDiv.id = "report-" + question.qid;
			// Set data attributes
			reportDiv.setAttribute("data-qname", "question" + qk);
			reportDiv.setAttribute("data-modal-target", "report-modal");
			reportDiv.setAttribute("data-modal-toggle", "report-modal");
			// Add classes
			reportDiv.classList.add("mt-[16px]", "items-center", "justify-center", "justify-items-center", "flex", "report_button", "cursor-pointer");
			// Create the paragraph element
			var reportText = document.createElement("p");
			reportText.classList.add("text-[14px]", "font-bold", "leading-[21px]", "text-[#4776ED]", "cursor-pointer");
			reportText.textContent = "<?= $this->lang->line("report_this_question") ?>";
			reportText.addEventListener('click', function() {
				clickReport(question.qid, question.question.replace(/(\.\.\/)+/g, '<?= base_url() ?>'),question.quid);
			});
			reportDiv.appendChild(reportText);

			// Thêm các phần tử vào cấu trúc cây DOM
			numQuestionElement.appendChild(spanElement);
			boxDetailElement.appendChild(numQuestionElement);
			boxDetailElement.appendChild(boxDetailResultDiv);
			boxDetailElement.appendChild(reportDiv);
			// Thêm các phần tử con vào boxDetailResultDiv
			if(question.description != ""){
			boxDetailResultDiv.appendChild(descriptionDiv);
			}
			var divBottom = document.createElement('div');
			divBottom.className = 'items-center justify-center flex';
			divBottom.appendChild(difficultyDiv);
			divBottom.appendChild(seperatorDiv);
			divBottom.appendChild(maxDiv);
			if(question.status == 0 || question.status == "0"){
				divBottom.appendChild(seperatorDiv.cloneNode(true));
				divBottom.appendChild(disableDiv);
			}
			boxDetailResultDiv.appendChild(divBottom);

			innerDivElement.appendChild(boxDetailElement);
			divElement.appendChild(innerDivElement);
			// Thêm các phần tử vào thẻ gốc
			boxDetailElement.appendChild(flexDivBookMark);
			boxDetailElement.appendChild(flexDivnote);
			boxDetailElement.appendChild(flexDiv);
			boxDetailElement.appendChild(prevQuestionDiv);
			boxDetailElement.appendChild(nextQuestionDiv);
			// Chèn divElement vào div có id là "test"
			var testDiv = document.getElementById(divID);
			testDiv.appendChild(divElement);
			idList++;

			button.onclick = function () {
				if (question.description !== "" && currentLang === "vietnamese") {
					translatedes(type + '_' + qk, question.qid);
				}
			};
		});
	}
	function createOptionDiv(className, imgSrc, text, index , classText ,iconClass) {
		// Tạo phần tử div
		var divElement = document.createElement('div');
		divElement.className = className;

		// Tạo phần tử img
		var imgElement = document.createElement('img');
		imgElement.src = '<?= base_url() ?>images/icons/' + imgSrc + '.svg';
		imgElement.alt = 'radio';
		imgElement.className = iconClass;

		// Tạo phần tử span
		var spanElement = document.createElement('span');
		spanElement.className = classText;

		// Tạo phần tử b trong span
		var bElement = document.createElement('b');
		bElement.classList.add('flex', 'items-center');
		bElement.innerHTML = index + ". ";

		// Tạo nội dung cho span
		spanElement.appendChild(bElement);
		spanElement.innerHTML += text;

		// Thêm các phần tử con vào phần tử div
		divElement.appendChild(imgElement);
		divElement.appendChild(spanElement);
		return divElement;
	}

	var num;
	var qid;
	var quid;
	function clickReport(qid, questionTitle, quid){
		$('#popup').removeClass('hidden');
		$('#report-content').val('');
		$('#question-content').html(questionTitle.toString().trim().replace("<p>","").replace("</p>",""));
		$('#question_quid').val(quid);
		$('#question_qid').val(qid);
	};
	function hideReport(){
		$('#popup').addClass('hidden');
	}
	$(document).ready(function() {
		// Tab initialization is handled by loadQuestions()
		$("#send-report").click(function() {
			var type = 1;
			var content = $('#report-content').val();
			var questionReport = $('#question-content').html();	
			var quid = $('#question_quid').val();
			var qid = $('#question_qid').val();
			var reportSection = $('input[name="report_section"]:checked').val();
			//qid = qid.replace("report", "");
			if (content == "") {
				return null;
			}
			const lines = content.split('\n');
			const nonEmptyLines = lines.filter(line => line.toString().trim() !== '');
			const htmlText = nonEmptyLines.map(line => line.replace(/\n/g, '<br>')).join('<br>');
			$.ajax({
				type: "POST",
				url: base_url + "report/send_report",
				data: {
					qid: qid,
					content: htmlText,
					type: type,
					questionReport: questionReport,
					quid: quid,
					reportSection: reportSection
				},
				beforeSend: function() {
					$('#close-modal').trigger('click');
				},
				success: function(data) {
					if (data == "Success") {
						$('#alert-report-success').fadeIn();
						setTimeout(() => {
							$('#alert-report-success').fadeOut();
						}, 5000);
					} else {
						$('#alert-report-error').fadeIn();
						setTimeout(() => {
							$('#alert-report-error').fadeOut();
						}, 5000);
					}
				},
				error: function(xhr, status, strErr) {
					$('#alert-report-error').fadeIn();
					setTimeout(() => {
						$('#alert-report-error').fadeOut();
					}, 5000);
				}
			});
		});
	})

	function addBookmark(qid, rid, type){
		if(type == 1)
		var image = document.getElementById("bookmark_wrong" + qid);
		else if(type == 3)
		var image = document.getElementById("bookmark_note" + qid);
		else
		var image = document.getElementById("bookmark" + qid);
		if(image.src == "<?= base_url() ?>images/icons/Bookmark%202.svg" || image.src == "<?= base_url() ?>images/icons/itcertprep/Bookmark%202.svg"){
			$.ajax({
				type: "POST",
				url: base_url + "Bookmarked_answers/un_bookmark",
				data: {
					qid: qid
				},
				beforeSend: function() {
				},
				success: function(data) {
					if (data == "success") {
						// result2 = result2.map((item, index) => (item["qid"] == qid ? null : item)).filter(Boolean);
						bookmarkArray = bookmarkArray.filter(item => item != qid);
						$('#bookmark_count').text( "("+bookmarkArray.length.toString()+")");
						image.src = "<?= base_url() ?>images/icons/Bookmark_2.svg";
						if(bookmarkArray.includes(qid)){
							if(type == 1)
							document.getElementById("bookmark_wrong" + qid).src = "<?= base_url() ?>images/icons/Bookmark_2.svg";
							else if(type == 3)
							document.getElementById("bookmark_note" + qid).src = "<?= base_url() ?>images/icons/Bookmark_2.svg";
							else
							document.getElementById("bookmark" + qid).src = "<?= base_url() ?>images/icons/Bookmark_2.svg";
						}
						var index = bookmarkArray.indexOf(qid);
						if (index > -1) { 
							bookmarkArray.splice(index, 1); 
						}
					} else {
						$('#alert-report-error').fadeIn();
						setTimeout(() => {
							$('#alert-report-error').fadeOut();
						}, 5000);
					}
				},
				error: function(xhr, status, strErr) {
					$('#alert-report-error').fadeIn();
					setTimeout(() => {
						$('#alert-report-error').fadeOut();
					}, 5000);
				}
			});
		}else{
			$.ajax({
				type: "POST",
				url: base_url + "Bookmarked_answers/add_bookmark",
				data: {
					qid: qid,
					rid: rid
				},
				beforeSend: function() {
				},
				success: function(data) {
					if (data == "success") {
						var foundItem = type == 3 ? questionNotes.find(item => item.qid == qid) : current_questions.find(item => item.qid == qid);
						// result2.unshift(foundItem);
						bookmarkArray.push(foundItem["qid"])
						$('#bookmark_count').text( "("+bookmarkArray.length.toString()+")");
						const SITE_ID = <?= SITE_ID ?>;
						image.src = SITE_ID == 2 
							? "<?= base_url() ?>images/icons/itcertprep/Bookmark 2.svg" 
							: "<?= base_url() ?>images/icons/Bookmark 2.svg";
							if (wrongArray.includes(qid)) {
								let basePath = SITE_ID == 2 
									? "<?= base_url() ?>images/icons/itcertprep/" 
									: "<?= base_url() ?>images/icons/";

								if (type == 1)
									document.getElementById("bookmark_wrong" + qid).src = basePath + "Bookmark 2.svg";
								else if (type == 3)
									document.getElementById("bookmark_note" + qid).src = basePath + "Bookmark 2.svg";
								else
									document.getElementById("bookmark" + qid).src = basePath + "Bookmark 2.svg";
							}
						// bookmarkArray.push(qid);
					} else {
						$('#alert-report-error').fadeIn();
						setTimeout(() => {
							$('#alert-report-error').fadeOut();
						}, 5000);
					}
				},
				error: function(xhr, status, strErr) {
					$('#alert-report-error').fadeIn();
					setTimeout(() => {
						$('#alert-report-error').fadeOut();
					}, 5000);
				}
			});
		}
	}
</script>
<!-- script popup practice khi empty -->
<script>
    $(document).ready(function () {
		$('.practice').click(function(e) {
			var premium = <?php echo $premium ? 'true' : 'false'; ?>;
			e.preventDefault();
			if (premium == false) {
				$("#popupPremium").removeClass("hidden");
				return null;
			}
		})
		$('.create-practice-btn').click(function(e) {
			var premium = <?php echo $premium ? 'true' : 'false'; ?>;
			e.preventDefault();
			if (premium == false) {
				$("#popupPremium").removeClass("hidden");
				return null;
			}
			var numQues = $('.num_question .num_item.selected input').val();
			var time = $('.test_time .num_item.selected input').val();
			var domain = $('.domain_item.selected').attr('data');
			var radio = $('input[name="inline-radio-group"]:checked').val();
			var difficulty = $('input[name="inline-radio-group-difficulty"]:checked').val();
			var domainArr = [];
			$('.domain_item.selected').each(function(index, element) {
				domainArr.push($(element).attr('data'));
			});
			var domain = domainArr.join(',');
			if (domain == 0 || domain == "0,0,0") {
				domain = '';
			}
			var data = {
				'numQues': numQues,
				'time': time,
				'domain': domain,
				'characteristic': radio,
				'difficulty': difficulty,
				'title': '<?= $category['category_name'] ?> ' + 'Practice Test ' + numQues + ' Questions',
			};
			$.ajax({
				type: "POST",
				url: base_url + "quiz/validate_select_practice",
				data: data,
				beforeSend: function() {
					HoldOn.open({
						theme: "sk-cube-grid",
					});
				},
				success: function(response) {
					HoldOn.close();
					if (response == "success") {
						window.location.href = base_url + 'quiz/quiz_detail_practice?' + $.param(data);
					} else {
						showErrorAlert(response);
					}
				},
				error: function(xhr, status, strErr) {
					HoldOn.close();
					showErrorAlert('<?= $this->lang->line('error_msg') ?>');
				}
			});
		});
		$('.domain_item').click(function(e) {
			e.preventDefault();
			if ($(this).hasClass('all')) {
				$('.domain_item').removeClass('selected');
			} else {
				$('.domain_item.all').removeClass('selected');
			}
			$(this).toggleClass('selected');
		});
		$('.section_toggle').click(function(e) {
			e.preventDefault();
			$('.expand_toggle').toggleClass('rotate-180');
			$('.advance_content').slideToggle();
		});
		$('.num_item input').click(function(e) {
			e.preventDefault();
		});
		$('.num_question .num_item').click(function(e) {
			$('.num_question .num_item').removeClass('selected');
			$(this).addClass('selected');
		});
		$('.test_time .num_item').click(function(e) {
			$('.test_time .num_item').removeClass('selected');
			$(this).addClass('selected');
		});
	});
	$(document).ready(function() {
		$('.focusImage').click(function() {
			var newSrc = $(this).attr('src');
			$('#modalImage').attr('src', newSrc);
		});
	});
</script>