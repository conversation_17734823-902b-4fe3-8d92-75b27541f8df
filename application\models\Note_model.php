<?php
class Note_model extends CI_Model
{
	public function __construct()
	{
		parent::__construct();
	}
	function list_note($cid)
	{
		$user = $this->session->userdata('logged_in');
		$query = $this->db->get('question_note');
		return $query->result_array();
	}
	function remove_note($id)
	{
		$this->db->where('id', $id);
		if ($this->db->delete('question_note')) {
			return true;
		} else {
			return false;
		}
	}
	function get_note($id)
	{
		$this->db->where('id', $id);
		$query = $this->db->get('question_note');
		return $query->row_array();
	}
	function update_note($data)
	{
		$userdata = array(
			'note' => $data['note'],
		);
		$this->db->where('id', $data['id']);
		if ($this->db->update('question_note', $userdata)) {
			return $data['id'];
		} else {
			return false;
		}
	}
	function insert_note($data, $uid = null)
	{
		if($uid == null){
			$user = $this->session->userdata('logged_in');
			$uid = $user['uid'];
		}
		$userdata = array(
			'uid' => $uid,
			'qid' => $data['qid'],
			'note' => $data['note'],
		);
		if ($this->db->insert('question_note', $userdata)) {
			return $this->db->insert_id();
		} else {
			return false;
		}
	}

	public function get_list_note_by_qid($qids)
	{
		if ($qids == null || count($qids) == 0) return [];
		$user = $this->session->userdata('logged_in');
		$query = $this->db->where_in('qid', $qids)->where('uid', $user['uid'])->select('id, qid')->get('question_note');
		$data = $query->result_array();
		$result = [];
		foreach ($data as $key => $value) {
			$result[$value['qid']] = $value['id'];
		}
		return $result;
	}

	public function get_list_note_user($cid, $index = -1, $search = null)
	{
		$user = $this->session->userdata('logged_in');
		$this->db->where('uid', $user['uid']);
		$this->db->join('savsoft_qbank', 'savsoft_qbank.qid=question_note.qid', 'left');
		$this->db->where("FIND_IN_SET('".$cid."', savsoft_qbank.cid)");

		// Apply search filter if provided
		if($search && !empty($search)) {
			$this->db->like('savsoft_qbank.question', $search);
		}

		$this->db->select('id, question_note.qid');
		$query = $this->db->get('question_note');
		$data = $query->result_array();
		$result = [];
		foreach ($data as $key => $value) {
			$result[$value['qid']] = $value['id'];
		}
		return $result;
	}

	public function get_question_by_list_note($notes, $search = null)
	{
		if($notes == null) return [];
		$query = $this->db->where_in('id', $notes)->select('qid')->get('question_note');
		$data = $query->result_array();
		$result = [];
		foreach ($data as $key => $value) {
			$result[] = $value['qid'];
		}

		$this->db->where_in('qid', $result);
		// Apply search filter if provided
		if($search && !empty($search)) {
			$this->db->like('question', $search);
		}
		$questions = $this->db->get('savsoft_qbank')->result_array();

		$index = 0;
		$filtered_questions = [];
		foreach ($questions as $key => $val) {
			//get right ans
			$right_ans = [];
			$get_options = $this->quiz_model->get_options_exam_tool($val['qid']);
			$val["options"] = $get_options;
			foreach ($get_options as $option => $opt_info) {
				if ($opt_info["score"] > 0) {
					array_push($right_ans, $opt_info["oid"]);
				}
			}
			$right_ans = implode(",", $right_ans);
			$val["correct"] = $right_ans;
			//user option - set empty for notes
			$user_options = [];
			$user_options = implode(",", $user_options);
			$val["userAns"] = $user_options;
			// $val["quid"] = $key["quid"];
			$difficultyStr = $this->result_model->getDifficultyStr($val);
			$val["difficulty"] = $difficultyStr;
			$filtered_questions[] = $val;
		}
		return $filtered_questions;
	}

	public function get_note_ajax($search, $start, $length) {
		// First, get the total count of filtered records
		$this->db->from('question_note');
		$this->db->join('savsoft_qbank', 'question_note.qid = savsoft_qbank.qid', 'left');
		$this->db->join('savsoft_users', 'question_note.uid = savsoft_users.uid', 'left');

		if($search != "" && $search != null){
			$this->db->group_start();
			$this->db->or_like('question_note.id', $search);
			$this->db->or_like('question_note.note', $search);
			$this->db->or_like('savsoft_users.email', $search);
			$this->db->or_like('savsoft_qbank.question', $search);
			$this->db->group_end();
		}

		$filtered_count = $this->db->count_all_results('', false); // false to keep the query for reuse

		// Now get the limited data
		$this->db->select('question_note.id, question_note.qid, note, savsoft_qbank.question, savsoft_users.email')->order_by('question_note.id', 'desc');
		$this->db->limit($length);
        $this->db->offset($start);
		$query = $this->db->get();
		$result = $query->result_array();

		$data = [];
		foreach ($result as $value) {
			$button = "<button class='question-btn' onclick='showQuestion(".$value['qid'].")' data-toggle='modal' data-target='#viewQuestion'><i class='fas fa-search'></i></button>";
			$data[] = [$value['id'], $value['email'], cut_string($value['question']), $value['note'], $button];
		}
		return ['data' => $data, 'count' => $filtered_count];
	}
}
