<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Response Format</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test API Response Format</h1>
    <p>This page tests the new API response format that includes question count.</p>

    <div class="test-section">
        <h3>Test Bookmark Questions API</h3>
        <button onclick="testBookmarkAPI()">Test Bookmark API</button>
        <div id="bookmark-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test Wrong Questions API</h3>
        <button onclick="testWrongAPI()">Test Wrong API</button>
        <div id="wrong-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test Note Questions API</h3>
        <button onclick="testNoteAPI()">Test Note API</button>
        <div id="note-result" class="result"></div>
    </div>

    <script>
        // Mock base URL - adjust this to your actual base URL
        const base_url = window.location.origin + '/scrumpass-exam/';

        function testBookmarkAPI() {
            $('#bookmark-result').html('Testing...');
            
            $.ajax({
                url: base_url + "Bookmarked_answers/get_bookmark_questions",
                type: "GET",
                data: {
                    index: -1,
                    search: ''
                },
                success: function(resp) {
                    const data = typeof resp === 'string' ? JSON.parse(resp) : resp;
                    let resultHtml = '<h4>Response Structure:</h4>';
                    
                    if(data && data.questions !== undefined && data.total_count !== undefined) {
                        resultHtml += '<div class="success">';
                        resultHtml += '<p><strong>✓ New format detected!</strong></p>';
                        resultHtml += '<p>Questions count: ' + (Array.isArray(data.questions) ? data.questions.length : 1) + '</p>';
                        resultHtml += '<p>Total count: ' + data.total_count + '</p>';
                        resultHtml += '<p>Response keys: ' + Object.keys(data).join(', ') + '</p>';
                        resultHtml += '</div>';
                    } else if(Array.isArray(data)) {
                        resultHtml += '<div class="error">';
                        resultHtml += '<p><strong>⚠ Legacy format detected</strong></p>';
                        resultHtml += '<p>Array length: ' + data.length + '</p>';
                        resultHtml += '</div>';
                    } else {
                        resultHtml += '<div class="error">';
                        resultHtml += '<p><strong>✗ Unexpected format</strong></p>';
                        resultHtml += '<p>Response: ' + JSON.stringify(data) + '</p>';
                        resultHtml += '</div>';
                    }
                    
                    $('#bookmark-result').html(resultHtml);
                },
                error: function(xhr, status, error) {
                    $('#bookmark-result').html('<div class="error">Error: ' + error + '</div>');
                }
            });
        }

        function testWrongAPI() {
            $('#wrong-result').html('Testing...');
            
            $.ajax({
                url: base_url + "Bookmarked_answers/get_wrong_questions",
                type: "GET",
                data: {
                    index: -1,
                    search: ''
                },
                success: function(resp) {
                    const data = typeof resp === 'string' ? JSON.parse(resp) : resp;
                    let resultHtml = '<h4>Response Structure:</h4>';
                    
                    if(data && data.questions !== undefined && data.total_count !== undefined) {
                        resultHtml += '<div class="success">';
                        resultHtml += '<p><strong>✓ New format detected!</strong></p>';
                        resultHtml += '<p>Questions count: ' + (Array.isArray(data.questions) ? data.questions.length : 1) + '</p>';
                        resultHtml += '<p>Total count: ' + data.total_count + '</p>';
                        resultHtml += '<p>Response keys: ' + Object.keys(data).join(', ') + '</p>';
                        resultHtml += '</div>';
                    } else if(Array.isArray(data)) {
                        resultHtml += '<div class="error">';
                        resultHtml += '<p><strong>⚠ Legacy format detected</strong></p>';
                        resultHtml += '<p>Array length: ' + data.length + '</p>';
                        resultHtml += '</div>';
                    } else {
                        resultHtml += '<div class="error">';
                        resultHtml += '<p><strong>✗ Unexpected format</strong></p>';
                        resultHtml += '<p>Response: ' + JSON.stringify(data) + '</p>';
                        resultHtml += '</div>';
                    }
                    
                    $('#wrong-result').html(resultHtml);
                },
                error: function(xhr, status, error) {
                    $('#wrong-result').html('<div class="error">Error: ' + error + '</div>');
                }
            });
        }

        function testNoteAPI() {
            $('#note-result').html('Testing...');
            
            $.ajax({
                url: base_url + "Bookmarked_answers/get_note_questions",
                type: "GET",
                data: {
                    index: -1,
                    search: ''
                },
                success: function(resp) {
                    const data = typeof resp === 'string' ? JSON.parse(resp) : resp;
                    let resultHtml = '<h4>Response Structure:</h4>';
                    
                    if(data && data.questions !== undefined && data.total_count !== undefined) {
                        resultHtml += '<div class="success">';
                        resultHtml += '<p><strong>✓ New format detected!</strong></p>';
                        resultHtml += '<p>Questions count: ' + (Array.isArray(data.questions) ? data.questions.length : 1) + '</p>';
                        resultHtml += '<p>Total count: ' + data.total_count + '</p>';
                        resultHtml += '<p>Response keys: ' + Object.keys(data).join(', ') + '</p>';
                        resultHtml += '</div>';
                    } else if(Array.isArray(data)) {
                        resultHtml += '<div class="error">';
                        resultHtml += '<p><strong>⚠ Legacy format detected</strong></p>';
                        resultHtml += '<p>Array length: ' + data.length + '</p>';
                        resultHtml += '</div>';
                    } else {
                        resultHtml += '<div class="error">';
                        resultHtml += '<p><strong>✗ Unexpected format</strong></p>';
                        resultHtml += '<p>Response: ' + JSON.stringify(data) + '</p>';
                        resultHtml += '</div>';
                    }
                    
                    $('#note-result').html(resultHtml);
                },
                error: function(xhr, status, error) {
                    $('#note-result').html('<div class="error">Error: ' + error + '</div>');
                }
            });
        }
    </script>
</body>
</html>
